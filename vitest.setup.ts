import "@testing-library/jest-dom";
import { configureAxe, toHaveNoViolations } from "jest-axe";

// Configure jest-axe for accessibility testing
const axe = configureAxe({
  rules: {
    // Disable color-contrast rule for testing (can be flaky in jsdom)
    "color-contrast": { enabled: false },
  },
});

// Make axe available globally for tests
global.axe = axe;

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

// Mock performance.now for testing
global.performance =
  global.performance ||
  ({
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    clearMarks: () => {},
    clearMeasures: () => {},
  } as any);
