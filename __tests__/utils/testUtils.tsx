/**
 * Test Utilities for Disc Golf Inventory Management System
 *
 * Shared utilities, mock data, and helper functions for testing components
 */

import React from "react";
import { render, RenderOptions } from "@testing-library/react";
import { vi } from "vitest";
import type { Disc, EnhancedFilterCriteria } from "@/lib/types";
import { DiscCondition, Location } from "@/lib/types";

// ============================================================================
// MOCK DATA
// ============================================================================

/**
 * Create a mock disc with default values and optional overrides
 */
export const createMockDisc = (overrides: Partial<Disc> = {}): Disc => ({
  id: "test-disc-1",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: {
    speed: 12,
    glide: 5,
    turn: -1,
    fade: 3,
  },
  color: "Blue",
  notes: "Test disc for unit testing",
  purchaseDate: new Date("2024-01-01"),
  purchasePrice: 20.99,
  currentLocation: Location.BAG,
  imageUrl: "https://example.com/test-disc.jpg",
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
  ...overrides,
});

/**
 * Collection of mock discs for testing
 */
export const mockDiscs: Disc[] = [
  createMockDisc({
    id: "disc-1",
    manufacturer: "Innova",
    mold: "Destroyer",
    plasticType: "Champion",
    weight: 175,
    condition: DiscCondition.NEW,
    color: "Blue",
  }),
  createMockDisc({
    id: "disc-2",
    manufacturer: "Discraft",
    mold: "Buzzz",
    plasticType: "ESP",
    weight: 180,
    condition: DiscCondition.GOOD,
    color: "Red",
    flightNumbers: { speed: 5, glide: 4, turn: -1, fade: 1 },
  }),
  createMockDisc({
    id: "disc-3",
    manufacturer: "Dynamic Discs",
    mold: "Judge",
    plasticType: "Classic Soft",
    weight: 174,
    condition: DiscCondition.WORN,
    color: "White",
    currentLocation: Location.STORAGE,
    flightNumbers: { speed: 2, glide: 4, turn: 0, fade: 1 },
  }),
];

/**
 * Mock filter criteria for testing
 */
export const mockFilterCriteria: EnhancedFilterCriteria = {
  searchTerm: "",
  manufacturers: [],
  conditions: [],
  locations: [],
  colors: [],
  plasticTypes: [],
  speedRange: { min: 1, max: 15 },
  glideRange: { min: 1, max: 7 },
  turnRange: { min: -5, max: 1 },
  fadeRange: { min: 0, max: 5 },
  weightRange: { min: 150, max: 180 },
};

/**
 * Mock filter options for testing
 */
export const mockFilterOptions = {
  manufacturers: ["Innova", "Discraft", "Dynamic Discs", "Latitude 64"],
  conditions: Object.values(DiscCondition),
  locations: Object.values(Location),
  colors: ["Blue", "Red", "White", "Yellow", "Green", "Orange"],
  plasticTypes: ["Champion", "ESP", "Classic Soft", "Star", "Z"],
};

// ============================================================================
// TEST UTILITIES
// ============================================================================

/**
 * Custom render function with common providers
 */
export const renderWithProviders = (ui: React.ReactElement, options?: Omit<RenderOptions, "wrapper">) => {
  // For now, just use the standard render
  // In the future, this could wrap with providers like React Query, etc.
  return render(ui, options);
};

/**
 * Mock function factory for consistent mock creation
 */
export const createMockFunction = <T extends (...args: any[]) => any>() => {
  return vi.fn<Parameters<T>, ReturnType<T>>();
};

/**
 * Helper to wait for async operations in tests
 */
export const waitForAsync = (ms: number = 0) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

/**
 * Mock localStorage for testing
 */
export const mockLocalStorage = () => {
  const store: Record<string, string> = {};

  return {
    getItem: vi.fn((key: string) => store[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete store[key];
    }),
    clear: vi.fn(() => {
      Object.keys(store).forEach((key) => delete store[key]);
    }),
    key: vi.fn((index: number) => Object.keys(store)[index] || null),
    get length() {
      return Object.keys(store).length;
    },
  };
};

/**
 * Mock Image component for testing
 */
export const MockImage = ({ alt, ...props }: any) => <img alt={alt} data-testid="mock-image" {...props} />;

/**
 * Helper to simulate user interactions
 */
export const userInteractions = {
  clickButton: (element: HTMLElement) => {
    element.click();
  },
  typeInInput: (input: HTMLInputElement, value: string) => {
    input.focus();
    input.value = value;
    input.dispatchEvent(new Event("input", { bubbles: true }));
    input.dispatchEvent(new Event("change", { bubbles: true }));
  },
  pressKey: (element: HTMLElement, key: string) => {
    element.dispatchEvent(new KeyboardEvent("keydown", { key, bubbles: true }));
  },
};

/**
 * Accessibility test helper
 */
export const testAccessibility = async (container: HTMLElement) => {
  const results = await global.axe(container);
  expect(results).toHaveNoViolations();
};

/**
 * Common test assertions
 */
export const assertions = {
  elementExists: (element: HTMLElement | null) => {
    expect(element).toBeInTheDocument();
  },
  elementHasText: (element: HTMLElement | null, text: string) => {
    expect(element).toHaveTextContent(text);
  },
  elementHasClass: (element: HTMLElement | null, className: string) => {
    expect(element).toHaveClass(className);
  },
  elementHasAttribute: (element: HTMLElement | null, attr: string, value?: string) => {
    if (value !== undefined) {
      expect(element).toHaveAttribute(attr, value);
    } else {
      expect(element).toHaveAttribute(attr);
    }
  },
};

// ============================================================================
// MOCK IMPLEMENTATIONS
// ============================================================================

/**
 * Mock Next.js Image component
 */
vi.mock("next/image", () => ({
  default: MockImage,
}));

/**
 * Mock Next.js router
 */
export const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
  pathname: "/",
  query: {},
  asPath: "/",
  route: "/",
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
};

vi.mock("next/navigation", () => ({
  useRouter: () => mockRouter,
  usePathname: () => "/",
  useSearchParams: () => new URLSearchParams(),
}));

/**
 * Mock useInventory hook
 */
export const mockInventoryHook = {
  discs: mockDiscs,
  loading: false,
  error: null,
  addDisc: vi.fn(),
  updateDisc: vi.fn(),
  deleteDisc: vi.fn(),
  getDisc: vi.fn(),
  clearError: vi.fn(),
};

vi.mock("@/hooks/useInventory", () => ({
  useInventory: () => mockInventoryHook,
}));

/**
 * Setup function to run before each test
 */
export const setupTest = () => {
  // Clear all mocks
  vi.clearAllMocks();

  // Reset localStorage mock
  Object.defineProperty(window, "localStorage", {
    value: mockLocalStorage(),
    writable: true,
  });

  // Reset router mock
  mockRouter.push.mockClear();
  mockRouter.replace.mockClear();
};

/**
 * Cleanup function to run after each test
 */
export const cleanupTest = () => {
  vi.clearAllMocks();
};
