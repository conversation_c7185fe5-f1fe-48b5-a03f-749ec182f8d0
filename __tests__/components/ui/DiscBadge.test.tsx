/**
 * Test suite for DiscBadge component
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen } from "@testing-library/react";
import { DiscBadge } from "@/components/ui/DiscBadge";
import { DiscCondition } from "@/lib/types";
import { setupTest, cleanupTest, testAccessibility } from "../../utils/testUtils";

describe("DiscBadge", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders with condition prop", () => {
      render(<DiscBadge condition={DiscCondition.NEW} />);
      
      const badge = screen.getByText("New");
      expect(badge).toBeInTheDocument();
    });

    it("renders with custom text", () => {
      render(<DiscBadge text="Custom Badge" />);
      
      const badge = screen.getByText("Custom Badge");
      expect(badge).toBeInTheDocument();
    });

    it("renders with both condition and custom text (text takes precedence)", () => {
      render(<DiscBadge condition={DiscCondition.NEW} text="Override Text" />);
      
      const badge = screen.getByText("Override Text");
      expect(badge).toBeInTheDocument();
      expect(screen.queryByText("New")).not.toBeInTheDocument();
    });

    it("renders with custom className", () => {
      render(<DiscBadge condition={DiscCondition.NEW} className="custom-badge" />);
      
      const badge = screen.getByText("New");
      expect(badge).toHaveClass("custom-badge");
    });
  });

  describe("Condition Variants", () => {
    it("renders NEW condition with correct styling", () => {
      render(<DiscBadge condition={DiscCondition.NEW} />);
      
      const badge = screen.getByText("New");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveAttribute("data-condition", "new");
    });

    it("renders GOOD condition with correct styling", () => {
      render(<DiscBadge condition={DiscCondition.GOOD} />);
      
      const badge = screen.getByText("Good");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveAttribute("data-condition", "good");
    });

    it("renders FAIR condition with correct styling", () => {
      render(<DiscBadge condition={DiscCondition.FAIR} />);
      
      const badge = screen.getByText("Fair");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveAttribute("data-condition", "fair");
    });

    it("renders WORN condition with correct styling", () => {
      render(<DiscBadge condition={DiscCondition.WORN} />);
      
      const badge = screen.getByText("Worn");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveAttribute("data-condition", "worn");
    });

    it("renders DAMAGED condition with correct styling", () => {
      render(<DiscBadge condition={DiscCondition.DAMAGED} />);
      
      const badge = screen.getByText("Damaged");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveAttribute("data-condition", "damaged");
    });
  });

  describe("Size Variants", () => {
    it("renders default size", () => {
      render(<DiscBadge condition={DiscCondition.NEW} />);
      
      const badge = screen.getByText("New");
      expect(badge).toBeInTheDocument();
    });

    it("renders small size", () => {
      render(<DiscBadge condition={DiscCondition.NEW} size="sm" />);
      
      const badge = screen.getByText("New");
      expect(badge).toBeInTheDocument();
    });

    it("renders large size", () => {
      render(<DiscBadge condition={DiscCondition.NEW} size="lg" />);
      
      const badge = screen.getByText("New");
      expect(badge).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(<DiscBadge condition={DiscCondition.NEW} />);
      await testAccessibility(container);
    });

    it("has proper ARIA attributes for condition", () => {
      render(<DiscBadge condition={DiscCondition.NEW} />);
      
      const badge = screen.getByText("New");
      expect(badge).toHaveAttribute("aria-label", "Disc condition: New");
    });

    it("has proper ARIA attributes for custom text", () => {
      render(<DiscBadge text="Custom Badge" />);
      
      const badge = screen.getByText("Custom Badge");
      expect(badge).toHaveAttribute("aria-label", "Badge: Custom Badge");
    });

    it("supports custom ARIA label", () => {
      render(<DiscBadge condition={DiscCondition.NEW} aria-label="Custom label" />);
      
      const badge = screen.getByText("New");
      expect(badge).toHaveAttribute("aria-label", "Custom label");
    });
  });

  describe("Edge Cases", () => {
    it("handles undefined condition gracefully", () => {
      render(<DiscBadge condition={undefined as any} />);
      
      // Should render empty or fallback content
      const badge = document.querySelector('[role="status"]') || document.querySelector('.badge');
      expect(badge).toBeInTheDocument();
    });

    it("handles empty text", () => {
      render(<DiscBadge text="" />);
      
      const badge = document.querySelector('[role="status"]') || document.querySelector('.badge');
      expect(badge).toBeInTheDocument();
    });

    it("handles very long text", () => {
      const longText = "This is a very long badge text that might overflow";
      render(<DiscBadge text={longText} />);
      
      const badge = screen.getByText(longText);
      expect(badge).toBeInTheDocument();
    });

    it("forwards additional props", () => {
      render(
        <DiscBadge 
          condition={DiscCondition.NEW} 
          data-testid="test-badge"
          title="Tooltip text"
        />
      );
      
      const badge = screen.getByText("New");
      expect(badge).toHaveAttribute("data-testid", "test-badge");
      expect(badge).toHaveAttribute("title", "Tooltip text");
    });
  });

  describe("Integration", () => {
    it("works with different condition values", () => {
      const conditions = Object.values(DiscCondition);
      
      conditions.forEach((condition) => {
        const { unmount } = render(<DiscBadge condition={condition} />);
        
        // Should render without errors
        const badge = document.querySelector('[role="status"]') || document.querySelector('.badge');
        expect(badge).toBeInTheDocument();
        
        unmount();
      });
    });

    it("maintains consistent styling across conditions", () => {
      const conditions = Object.values(DiscCondition);
      
      conditions.forEach((condition) => {
        const { unmount } = render(<DiscBadge condition={condition} />);
        
        const badge = document.querySelector('[role="status"]') || document.querySelector('.badge');
        expect(badge).toHaveClass("inline-flex"); // Base badge class
        
        unmount();
      });
    });
  });
});
